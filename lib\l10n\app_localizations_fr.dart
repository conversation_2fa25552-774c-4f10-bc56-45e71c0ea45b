// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for French (`fr`).
class AppLocalizationsFr extends AppLocalizations {
  AppLocalizationsFr([String locale = 'fr']) : super(locale);

  @override
  String get appName => 'Banque des Employés';

  @override
  String get appSubtitle => 'Système de Gestion des Employés Bancaires';

  @override
  String get login => 'Se connecter';

  @override
  String get rememberMe => 'Se souvenir de moi';

  @override
  String get uncheckrememberme => 'Désélectionnez \'Se souvenir de moi\' car la biométrie est activée';

  @override
  String get enteremail => 'Entrez l\'email';

  @override
  String get validemail => 'Veuillez entrer un email valide';

  @override
  String get enterpassword => 'Entrez le mot de passe';

  @override
  String get correctpassword => 'Le mot de passe doit contenir au moins 6 caractères';

  @override
  String get securityInformation => 'Informations de sécurité';

  @override
  String get rememberMeInfo => 'Lorsque vous activez cette option, vos identifiants de connexion seront stockés de manière sécurisée sur votre appareil pour faciliter les connexions futures.\n\nVous pouvez désactiver cette option à tout moment dans les paramètres de l\'application.';

  @override
  String get understood => 'Compris';

  @override
  String get identification => 'Identification';

  @override
  String get directmanager => 'Manager direct';

  @override
  String get quickactions => 'Actions rapides';

  @override
  String get myprofile => 'Mon profil';

  @override
  String get leaveTypes => 'Types de congés';

  @override
  String get myLeaveRequests => 'Mes demandes';

  @override
  String get leaveApprovals => 'Approbations';

  @override
  String get retry => 'Réessayer';

  @override
  String get settings => 'Paramètres';

  @override
  String get appSettings => 'Paramètres de l\'application';

  @override
  String get customizeSettings => 'Personnalisez les paramètres de l\'application selon vos besoins';

  @override
  String get accountSettings => 'Paramètres du compte';

  @override
  String get changePassword => 'Changer le mot de passe';

  @override
  String get importantNotes => 'Notes importantes';

  @override
  String get changePasswordSubtitle => 'Mettez à jour votre mot de passe';

  @override
  String get securitySettings => 'Paramètres de sécurité';

  @override
  String get biometricLogin => 'Connexion biométrique';

  @override
  String get biometricEnabledSubtitle => 'Activé - Vous pouvez vous connecter avec la biométrie';

  @override
  String get biometricDisabledSubtitle => 'Désactivé - Utilisez uniquement le mot de passe';

  @override
  String get appearanceSettings => 'Paramètres d\'apparence';

  @override
  String get changeLanguage => 'Changer la langue';

  @override
  String get changeLanguageSubtitle => 'Choisissez votre langue préférée pour l\'application';

  @override
  String get selectLanguage => 'Sélectionner la langue';

  @override
  String get biometricEnabled => 'Connexion biométrique activée avec succès';

  @override
  String get biometricDisabled => 'Connexion biométrique désactivée avec succès';

  @override
  String get biometricSetup => 'Configuration de la connexion biométrique';

  @override
  String get biometricSetupMessage => 'Pour activer la connexion biométrique, veuillez saisir vos identifiants de connexion:';

  @override
  String get enable => 'Activer';

  @override
  String get languageChanged => 'Langue changée avec succès';

  @override
  String get languageChangeError => 'Erreur lors du changement de langue';

  @override
  String get cancel => 'Annuler';

  @override
  String get enterEmail => 'Entrez l\'email';

  @override
  String get enterPassword => 'Entrez le mot de passe';
}
