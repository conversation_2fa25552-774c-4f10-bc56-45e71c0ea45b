{"@@locale": "en", "appName": "Employee Bank", "appSubtitle": "Banking Employee Management System", "login": "<PERSON><PERSON>", "rememberMe": "Remember Me", "uncheckrememberme": "Uncheck 'Remember Me' because biometric is enabled", "enteremail": "Enter email", "validemail": "Please enter a valid email", "enterpassword": "Enter password", "correctpassword": "Password must be at least 6 characters", "securityInformation": "Security Information", "rememberMeInfo": "When you enable this option, your login credentials will be securely stored on your device to make future logins easier.\n\nYou can disable this option at any time from the app settings.", "understood": "Understood", "identification": "Identification", "directmanager": "Direct Manager", "quickactions": "Quick Actions", "myprofile": "My Profile", "leaveTypes": "Leave Types", "myLeaveRequests": "My Requests", "leaveApprovals": "Approvals", "retry": "Retry", "settings": "Settings", "appSettings": "App Settings", "customizeSettings": "Customize app settings according to your needs", "accountSettings": "Account <PERSON><PERSON>", "changePassword": "Change Password", "importantNotes": "Important Notes", "changePasswordSubtitle": "Update your password", "securitySettings": "Security Settings", "biometricLogin": "Biometric Login", "biometricEnabledSubtitle": "Enabled - You can login with biometric", "biometricDisabledSubtitle": "Disabled - Use password only", "appearanceSettings": "Appearance Settings", "changeLanguage": "Change Language", "changeLanguageSubtitle": "Choose your preferred app language", "selectLanguage": "Select Language", "biometricEnabled": "Biometric login enabled successfully", "biometricDisabled": "Biometric login disabled successfully", "biometricSetup": "Setup Biometric Login", "biometricSetupMessage": "To enable biometric login, please enter your login credentials:", "enable": "Enable", "languageChanged": "Language changed successfully", "languageChangeError": "Error occurred while changing language", "cancel": "Cancel", "enterEmail": "Enter email", "enterPassword": "Enter password"}